import numpy as np

# ========== CONFIGURAÇÃO DAS CORES DA BARRA DE VIDA ==========
# Baseado na análise da imagem life.png

LIFE_COLOR_RANGE = {
    'name': 'life_health_bar',
    'lower_hsv': np.array([np.int64(0), np.int64(0), np.int64(0)]),
    'upper_hsv': np.array([np.int64(178), np.int64(255), np.int64(255)]),
    'description': 'Cores da barra de vida dos aliados'
}

# Range alternativo mais amplo para detecção
LIFE_COLOR_RANGE_BROAD = {
    'name': 'life_health_bar_broad',
    'lower_hsv': np.array([85, 100, 100]),  # Azul-ciano com boa saturação
    'upper_hsv': np.array([115, 255, 255]), # Cobertura ampla de azuis
    'description': 'Range amplo para barras de vida azuis'
}

# Função para testar se uma cor está no range
def is_life_color(hsv_pixel, use_broad=False):
    """
    Verifica se um pixel HSV corresponde às cores da barra de vida
    
    Args:
        hsv_pixel: Pixel no formato HSV (H, S, V)
        use_broad: Se True, usa o range mais amplo
    
    Returns:
        bool: True se a cor corresponde à barra de vida
    """
    color_range = LIFE_COLOR_RANGE_BROAD if use_broad else LIFE_COLOR_RANGE
    
    return (color_range['lower_hsv'][0] <= hsv_pixel[0] <= color_range['upper_hsv'][0] and
            color_range['lower_hsv'][1] <= hsv_pixel[1] <= color_range['upper_hsv'][1] and
            color_range['lower_hsv'][2] <= hsv_pixel[2] <= color_range['upper_hsv'][2])
