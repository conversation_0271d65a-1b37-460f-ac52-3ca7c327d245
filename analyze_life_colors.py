#!/usr/bin/env python3
"""
Script para analisar as cores da imagem life.png e extrair os valores RGB/HSV
para usar como referência na detecção de aliados.
"""

import cv2
import numpy as np
from PIL import Image
import os

def analyze_life_image():
    """Analisa a imagem life.png e extrai informações de cores"""
    
    if not os.path.exists('life.png'):
        print("❌ Arquivo life.png não encontrado!")
        return None
    
    # Carrega a imagem usando PIL
    pil_image = Image.open('life.png')
    print(f"📊 Dimensões da imagem: {pil_image.size}")
    print(f"📊 Modo da imagem: {pil_image.mode}")
    
    # Converte para array numpy
    img_array = np.array(pil_image)
    
    # Se a imagem tem canal alpha, remove
    if img_array.shape[2] == 4:
        img_array = img_array[:, :, :3]
    
    # Converte para OpenCV (BGR)
    img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
    img_hsv = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2HSV)
    
    print(f"📊 Shape da imagem: {img_bgr.shape}")
    
    # Analisa cores únicas
    pixels = img_bgr.reshape(-1, 3)
    unique_colors = np.unique(pixels, axis=0)
    
    print(f"📊 Número de cores únicas: {len(unique_colors)}")
    print("\n🎨 Principais cores encontradas (BGR):")
    
    # Mostra as primeiras 10 cores mais comuns
    colors_count = {}
    for pixel in pixels:
        color_key = tuple(pixel)
        colors_count[color_key] = colors_count.get(color_key, 0) + 1
    
    # Ordena por frequência
    sorted_colors = sorted(colors_count.items(), key=lambda x: x[1], reverse=True)
    
    color_ranges = []
    
    for i, (color_bgr, count) in enumerate(sorted_colors[:10]):
        # Converte BGR para RGB para exibição
        color_rgb = (color_bgr[2], color_bgr[1], color_bgr[0])
        
        # Converte para HSV
        bgr_pixel = np.uint8([[color_bgr]])
        hsv_pixel = cv2.cvtColor(bgr_pixel, cv2.COLOR_BGR2HSV)[0][0]
        
        percentage = (count / len(pixels)) * 100
        
        print(f"  {i+1:2d}. BGR: {color_bgr} | RGB: {color_rgb} | HSV: {tuple(hsv_pixel)} | {percentage:.1f}%")
        
        # Cria ranges para as cores mais frequentes (primeiras 5)
        if i < 5:
            # Cria um range de tolerância para HSV
            h, s, v = hsv_pixel
            
            # Tolerâncias
            h_tolerance = 10
            s_tolerance = 50
            v_tolerance = 50
            
            lower_hsv = np.array([
                max(0, h - h_tolerance),
                max(0, s - s_tolerance),
                max(0, v - v_tolerance)
            ])
            
            upper_hsv = np.array([
                min(179, h + h_tolerance),
                min(255, s + s_tolerance),
                min(255, v + v_tolerance)
            ])
            
            color_ranges.append({
                'name': f'life_color_{i+1}',
                'bgr': color_bgr,
                'rgb': color_rgb,
                'hsv': tuple(hsv_pixel),
                'lower_hsv': tuple(lower_hsv),
                'upper_hsv': tuple(upper_hsv),
                'percentage': percentage
            })
    
    return color_ranges

def generate_color_detection_code(color_ranges):
    """Gera código Python para detecção das cores"""
    
    if not color_ranges:
        return ""
    
    code = """
# ========== CORES DA IMAGEM LIFE.PNG ==========
LIFE_COLOR_RANGES = [
"""
    
    for color_range in color_ranges:
        code += f"""    {{
        'name': '{color_range['name']}',
        'lower_hsv': np.array({list(color_range['lower_hsv'])}),
        'upper_hsv': np.array({list(color_range['upper_hsv'])}),
        'bgr': {color_range['bgr']},
        'percentage': {color_range['percentage']:.1f}
    }},
"""
    
    code += "]\n"
    
    return code

if __name__ == "__main__":
    print("🔍 Analisando cores da imagem life.png...")
    print("=" * 60)
    
    color_ranges = analyze_life_image()
    
    if color_ranges:
        print("\n" + "=" * 60)
        print("📝 Código gerado para detecção:")
        print("=" * 60)
        
        generated_code = generate_color_detection_code(color_ranges)
        print(generated_code)
        
        # Salva o código em um arquivo
        with open('life_colors_config.py', 'w', encoding='utf-8') as f:
            f.write("import numpy as np\n\n")
            f.write(generated_code)
        
        print("✅ Configuração salva em 'life_colors_config.py'")
    else:
        print("❌ Não foi possível analisar a imagem")
