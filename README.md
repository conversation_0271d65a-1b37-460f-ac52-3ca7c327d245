# LoL DirectInput Multi Bot

Um bot avançado e completo para League of Legends com múltiplas funcionalidades usando DirectInput.

## 🎮 Funcionalidades

### 🎯 **Tecla I + Detecção de Barras de Vida**
- ✅ Mantém a tecla I pressionada continuamente
- ✅ **NOVO**: Detecta barras de vida dos aliados baseado na imagem `life.png`
- ✅ Clica automaticamente com **botão direito** nas barras de vida encontradas para seguir aliados
- ✅ Controle independente (ativar/desativar)

### ⚔️ **Farm (SHIFT Esquerdo)**
- ✅ Mantém SHIFT esquerdo pressionado para atacar
- ✅ Ideal para farm automático
- ✅ Funciona independentemente dos outros bots

### 🖱️ **Movimentação do Mouse**
- ✅ Move o mouse entre posições configuradas
- ✅ Interface para capturar posições facilmente
- ✅ Movimentação automática a cada 2 segundos
- ✅ Configuração visual em tempo real

### 🔧 **Recursos Gerais**
- ✅ **DirectInput**: Funciona em jogos que bloqueiam input normal
- ✅ **Sem dependências obrigatórias**: Usa apenas Python nativo
- ✅ **Interface gráfica completa**: Controle fácil e organizado
- ✅ **Hotkey ESC**: Para todos os bots rapidamente
- ✅ **Controle independente**: Cada bot funciona separadamente
- ✅ **Seguro**: Libera todas as teclas automaticamente ao parar
- ✅ **Teste integrado**: Botão para testar DirectInput

## Instalação

1. Certifique-se de ter Python 3.7+ instalado
2. **Não precisa instalar nada mais!** O bot usa apenas bibliotecas nativas do Python

## Como usar

1. Execute o script:
```bash
python lol_f2_bot.py
```

2. Uma janela será aberta com os controles do bot

3. **Configure o que precisar:**
   - **Tecla I**: Clique "🎮 Ativar Tecla I"
   - **Farm**: Clique "⚔️ Ativar Farm" (SHIFT esquerdo)
   - **Mouse**: Clique "⚙️ Configurar Posições" primeiro, depois "🖱️ Ativar Mouse"

4. **Teste primeiro** (opcional): Clique "🧪 Testar DirectInput (3s)"

5. **Abra o League of Legends** e use os bots conforme necessário

6. Para parar:
   - Clique em "Parar" na interface
   - Ou pressione ESC (hotkey global)
   - Ou feche a janela

## 🎛️ Controles

### **Tecla I**
- **🎮 Ativar Tecla I**: Liga/desliga o bot da tecla I
- **⏹️ Parar I**: Para especificamente o bot da tecla I

### **Farm (SHIFT)**
- **⚔️ Ativar Farm**: Liga/desliga o bot de farm (SHIFT esquerdo)
- **⏹️ Parar Farm**: Para especificamente o bot de farm

### **Mouse**
- **🖱️ Ativar Mouse**: Liga/desliga a movimentação automática
- **⚙️ Configurar Posições**: Abre janela para configurar posições do mouse
- **⏹️ Parar Mouse**: Para especificamente o bot do mouse

### **Controles Gerais**
- **🧪 Testar DirectInput**: Testa por 3 segundos
- **🛑 Parar Todos**: Para todos os bots de uma vez
- **ESC**: Hotkey global para parar todos os bots

## 🚀 Recursos Avançados

- **🎯 Controle independente**: Cada bot funciona separadamente
- **🖱️ Configuração visual do mouse**: Interface para capturar posições
- **📊 Status em tempo real**: Mostra quais bots estão ativos
- **🔄 DirectInput nativo**: Funciona em qualquer jogo
- **🛡️ Segurança**: Libera todas as teclas ao fechar
- **⚡ Performance**: Threads separadas para cada função
- **👁️ Detecção de barras de vida**: Usa visão computacional baseada na imagem `life.png`

## 🎨 Detecção de Barras de Vida

O bot agora inclui um sistema avançado de detecção de barras de vida dos aliados:

### Como funciona:
1. **Análise da imagem `life.png`**: O bot analisa as cores da barra de vida fornecida
2. **Detecção em tempo real**: Procura essas cores na tela durante o jogo
3. **Clique direito automático**: Clica com botão direito nas barras de vida encontradas para seguir aliados
4. **Filtros inteligentes**: Remove falsos positivos usando proporção e tamanho

### Como usar:
1. **Pressiona F10** para ativar o bot
2. **Pressiona tecla I** (DirectInput)
3. **Captura a tela inteira** procurando barras de vida
4. **Detecta barras** usando as cores da `life.png`
5. **Clica com botão direito na maior barra** encontrada para seguir o aliado
6. **Repete o ciclo** a cada 5 segundos

### Arquivos relacionados:
- `life.png` - Imagem de referência da barra de vida
- `test_life_detection.py` - Script para testar a detecção (pressione C para testar clique direito)
- `analyze_life_colors.py` - Analisa as cores da imagem life.png

## Segurança

- O bot libera automaticamente a tecla F2 quando parado
- Funciona apenas enquanto a aplicação estiver rodando
- Não interfere com outros jogos ou aplicações

## Requisitos

- Python 3.7+
- Windows (para DirectInput)
- **Nenhuma biblioteca externa necessária!**

## Aviso

Use este bot de acordo com os termos de serviço do League of Legends. O autor não se responsabiliza pelo uso inadequado da ferramenta.
