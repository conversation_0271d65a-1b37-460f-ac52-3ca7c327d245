import time
import ctypes

# Tentar importar dependências opcionais
try:
    import numpy as np
    import cv2
    import pyautogui
    from PIL import ImageGrab
    import win32gui  # type: ignore
    VISION_AVAILABLE = True
except ImportError:
    VISION_AVAILABLE = False
    print("⚠️  Dependências de visão computacional não encontradas.")
    print("   O bot funcionará apenas com DirectInput básico.")

# ========== CONFIGURAÇÃO DAS CORES DA BARRA DE VIDA ==========
# Baseado na análise da imagem life.png
if VISION_AVAILABLE:
    # Cores principais da barra de vida (azul-ciano)
    LIFE_COLOR_RANGES = [
        {
            'name': 'life_primary',
            'lower_hsv': np.array([95, 150, 150]),  # Azul-ciano principal
            'upper_hsv': np.array([105, 255, 255]),
            'description': 'Cor principal da barra de vida'
        },
        {
            'name': 'life_secondary',
            'lower_hsv': np.array([90, 120, 120]),  # Range mais amplo
            'upper_hsv': np.array([110, 255, 255]),
            'description': 'Cor secundária da barra de vida'
        },
        {
            'name': 'life_broad',
            'lower_hsv': np.array([85, 100, 100]),  # Range muito amplo para capturar variações
            'upper_hsv': np.array([115, 255, 255]),
            'description': 'Range amplo para barras de vida azuis'
        }
    ]

# ========== CONFIGURAÇÕES DO DIRECTINPUT ==========
class KeyBdInput(ctypes.Structure):
    _fields_ = [("wVk", ctypes.c_ushort),
                ("wScan", ctypes.c_ushort),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))]

class HardwareInput(ctypes.Structure):
    _fields_ = [("uMsg", ctypes.c_ulong),
                ("wParamL", ctypes.c_short),
                ("wParamH", ctypes.c_ushort)]

class MouseInput(ctypes.Structure):
    _fields_ = [("dx", ctypes.c_long),
                ("dy", ctypes.c_long),
                ("mouseData", ctypes.c_ulong),
                ("dwFlags", ctypes.c_ulong),
                ("time", ctypes.c_ulong),
                ("dwExtraInfo", ctypes.POINTER(ctypes.c_ulong))]

class Input_I(ctypes.Union):
    _fields_ = [("ki", KeyBdInput),
                ("mi", MouseInput),
                ("hi", HardwareInput)]

class Input(ctypes.Structure):
    _fields_ = [("type", ctypes.c_ulong),
                ("ii", Input_I)]

# Códigos DirectInput para as teclas
DIK_I = 0x17  # Tecla I
DIK_A = 0x1E  # Tecla A

def PressKey(hexKeyCode):
    """Pressiona uma tecla usando DirectInput"""
    extra = ctypes.c_ulong(0)
    ii_ = Input_I()
    ii_.ki = KeyBdInput(0, hexKeyCode, 0x0008, 0, ctypes.pointer(extra))
    x = Input(ctypes.c_ulong(1), ii_)
    ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))

def ReleaseKey(hexKeyCode):
    """Solta uma tecla usando DirectInput"""
    extra = ctypes.c_ulong(0)
    ii_ = Input_I()
    ii_.ki = KeyBdInput(0, hexKeyCode, 0x0008 | 0x0002, 0, ctypes.pointer(extra))
    x = Input(ctypes.c_ulong(1), ii_)
    ctypes.windll.user32.SendInput(1, ctypes.pointer(x), ctypes.sizeof(x))

def PressMultipleKeys(key_codes):
    """Pressiona múltiplas teclas simultaneamente"""
    for key_code in key_codes:
        PressKey(key_code)

def ReleaseMultipleKeys(key_codes):
    """Solta múltiplas teclas simultaneamente"""
    for key_code in key_codes:
        ReleaseKey(key_code)

# ========== SISTEMA DIRECTINPUT SIMPLES ==========
class LoLAutoFollow:
    def __init__(self):
        self.pressing = False
        self.cycle_count = 0
        self.vision_enabled = VISION_AVAILABLE
        self.keys_to_press = [DIK_I, DIK_A]  # Teclas I e A simultaneamente

        # Configurações de visão (se disponível)
        if VISION_AVAILABLE:
            self.following = False
            self.minimap_region = (0, 0, 0, 0)  # Será definido automaticamente
            self.game_window = None
            self.screen_region = (0, 0, 0, 0)   # Região da tela para buscar barras de vida
        
    def find_game_window(self):
        """Encontra a janela do League of Legends"""
        if not VISION_AVAILABLE:
            return False

        def callback(hwnd, extra):
            if win32gui.IsWindowVisible(hwnd):
                window_text = win32gui.GetWindowText(hwnd)
                if "League of Legends" in window_text:
                    extra.append(hwnd)

        windows = []
        win32gui.EnumWindows(callback, windows)

        if windows:
            self.game_window = windows[0]
            rect = win32gui.GetWindowRect(self.game_window)
            print(f"✅ Janela do LoL encontrada: {rect}")
            return True
        return False
    
    def get_minimap_region(self):
        """Define a região do minimapa baseado na resolução da tela"""
        if not VISION_AVAILABLE:
            return None

        if not self.game_window:
            if not self.find_game_window():
                return None

        # Obtém as dimensões da janela do jogo
        x, y, right, bottom = win32gui.GetWindowRect(self.game_window)
        width = right - x
        height = bottom - y

        # Calcula a região do minimapa (canto inferior direito)
        minimap_width = int(width * 0.15)  # 15% da largura
        minimap_height = int(height * 0.2)  # 20% da altura

        minimap_x = right - minimap_width - 10  # Margem de 10 pixels
        minimap_y = bottom - minimap_height - 10

        self.minimap_region = (minimap_x, minimap_y, minimap_width, minimap_height)
        return self.minimap_region

    def capture_minimap(self):
        """Captura a tela do minimapa"""
        if not VISION_AVAILABLE:
            return None

        if self.minimap_region == (0, 0, 0, 0):
            self.get_minimap_region()

        screenshot = ImageGrab.grab(bbox=(
            self.minimap_region[0],
            self.minimap_region[1],
            self.minimap_region[0] + self.minimap_region[2],
            self.minimap_region[1] + self.minimap_region[3]
        ))

        return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)

    def capture_screen_region(self, region=None):
        """Captura uma região da tela ou a tela inteira"""
        if not VISION_AVAILABLE:
            return None

        if region is None:
            # Captura a tela inteira
            screenshot = ImageGrab.grab()
        else:
            # Captura região específica
            x, y, w, h = region
            screenshot = ImageGrab.grab(bbox=(x, y, x + w, y + h))

        return cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
    
    def find_life_bars(self, image):
        """Encontra barras de vida dos aliados usando as cores da life.png"""
        if not VISION_AVAILABLE or image is None:
            return [], None

        # Converte para HSV para melhor detecção de cor
        hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)

        # Cria máscara combinada para todas as cores da barra de vida
        combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)

        for color_range in LIFE_COLOR_RANGES:
            # Cria máscara para este range de cor
            mask = cv2.inRange(hsv, color_range['lower_hsv'], color_range['upper_hsv'])
            combined_mask = cv2.bitwise_or(combined_mask, mask)

        # Aplica operações morfológicas para limpar a máscara
        kernel = np.ones((3, 3), np.uint8)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
        combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)

        # Encontra contornos
        contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        life_bars = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > 20:  # Filtra contornos muito pequenos
                x, y, w, h = cv2.boundingRect(contour)

                # Filtra por proporção (barras de vida são mais largas que altas)
                aspect_ratio = w / h if h > 0 else 0
                if 1.5 <= aspect_ratio <= 10:  # Barras de vida típicas
                    center_x, center_y = x + w//2, y + h//2
                    life_bars.append({
                        'center': (center_x, center_y),
                        'bbox': (x, y, w, h),
                        'area': area,
                        'aspect_ratio': aspect_ratio
                    })

        # Ordena por área (barras maiores primeiro)
        life_bars.sort(key=lambda x: x['area'], reverse=True)

        return life_bars, combined_mask
    
    def click_on_life_bar(self, life_bar):
        """Clica na barra de vida do aliado encontrado"""
        if not VISION_AVAILABLE:
            return False

        # Usa as coordenadas do centro da barra de vida
        screen_x, screen_y = life_bar['center']

        # Salva posição atual do mouse
        original_pos = pyautogui.position()

        try:
            # Move e clica com botão direito na barra de vida
            pyautogui.moveTo(screen_x, screen_y, duration=0.1)
            pyautogui.rightClick()

            # Volta o mouse para posição original
            pyautogui.moveTo(original_pos.x, original_pos.y, duration=0.1)

            print(f"✅ Clicou com botão direito na barra de vida em ({screen_x}, {screen_y}) - Área: {life_bar['area']:.0f}, Proporção: {life_bar['aspect_ratio']:.1f}")
            return True

        except Exception as e:
            print(f"❌ Erro ao clicar: {e}")
            return False

    def follow_nearest_ally(self):
        """Encontra e segue o aliado mais próximo baseado nas barras de vida"""
        if not VISION_AVAILABLE:
            print("🔍 Visão computacional não disponível")
            return False

        # Captura a tela inteira para procurar barras de vida
        screen_img = self.capture_screen_region()
        if screen_img is None:
            print("❌ Não foi possível capturar a tela")
            return False

        life_bars, _ = self.find_life_bars(screen_img)

        if not life_bars:
            print("🔍 Nenhuma barra de vida encontrada na tela")
            return False

        # Pega a primeira barra de vida (maior área)
        target_life_bar = life_bars[0]

        print(f"🎯 Barras de vida encontradas: {len(life_bars)} | Seguindo a maior")
        return self.click_on_life_bar(target_life_bar)
    
    def check_key_press(self):
        """Verifica se F10 foi pressionado para ligar/desligar"""
        VK_F10 = 0x79
        if ctypes.windll.user32.GetAsyncKeyState(VK_F10) & 0x8000:
            self.pressing = not self.pressing
            status = "LIGADO" if self.pressing else "DESLIGADO"
            print(f"\n🔧 Bot {status} (F10)")
            time.sleep(0.5)
            return True
        return False
    
    def check_exit_key(self):
        """Verifica se F9 foi pressionado para sair"""
        VK_F9 = 0x78
        if ctypes.windll.user32.GetAsyncKeyState(VK_F9) & 0x8000:
            print("\n🛑 Saindo do programa...")
            return True
        return False
    
    def run(self):
        """Executa o sistema principal"""
        print("=== SISTEMA AUTO-FOLLOW LOL ===")

        if VISION_AVAILABLE:
            print("✅ Modo COMPLETO - DirectInput + Detecção de Barras de Vida")
            print("Requisitos:")
            print("- League of Legends em modo TELA CHEIA ou JANELA SEM BARDA")
            print("- Resolução 1920x1080 recomendada")
            print("- Detecta barras de vida dos aliados baseado na imagem life.png")
        else:
            print("⚡ Modo BÁSICO - Apenas DirectInput")
            print("Funcionalidades:")
            print("- Pressiona tecla I continuamente")
            print("- Pressiona tecla A continuamente")
            print("- Não precisa de dependências extras")

        print()
        print("Teclas de controle:")
        print("  F10 - Ligar/Desligar o bot")
        print("  F9  - Sair do programa")
        print()

        # Tenta encontrar a janela do jogo (apenas se visão disponível)
        if VISION_AVAILABLE:
            if not self.find_game_window():
                print("⚠️  Janela do League of Legends não encontrada!")
                print("   Continuando em modo básico (apenas teclas I e A)")

        print("✅ Pronto! Pressione F10 para iniciar...")
        
        try:
            while True:
                if self.check_exit_key():
                    break
                
                if self.check_key_press():
                    if self.pressing:
                        self.cycle_count += 1
                        print(f"\n🔄 Ciclo {self.cycle_count} - Iniciando...")
                
                if self.pressing:
                    # Fase 1: Pressiona I e A simultaneamente
                    print("🎮 Pressionando teclas I e A simultaneamente...")
                    PressMultipleKeys(self.keys_to_press)
                    time.sleep(0.1)
                    ReleaseMultipleKeys(self.keys_to_press)

                    # Fase 2: Usa detecção de barras de vida (se disponível)
                    if VISION_AVAILABLE:
                        print("👀 Procurando barras de vida dos aliados...")
                        if self.follow_nearest_ally():
                            if hasattr(self, 'following'):
                                self.following = True
                            print("✅ Seguindo aliado com sucesso!")
                        else:
                            print("⚠️  Não foi possível encontrar barra de vida, usando apenas teclas I e A")
                    else:
                        print("⚡ Modo básico - apenas teclas I e A ativas")
                    
                    # CICLO CONTÍNUO - Executa imediatamente novamente
                    # Verifica teclas rapidamente entre ciclos
                    for _ in range(5):  # Verifica a cada 0.1s por 0.5s total
                        if self.check_exit_key():
                            return
                        if self.check_key_press():
                            if not self.pressing:
                                print("\n⏸️  Bot pausado")
                                break
                        time.sleep(0.1)
                    
                    print("🔄 Próximo ciclo...")
                
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n\n⏹️  Interrompido pelo usuário")
        except Exception as e:
            print(f"\n❌ Erro: {e}")
        finally:
            ReleaseMultipleKeys(self.keys_to_press)
            print("\n=== PROGRAMA FINALIZADO ===")

# ========== INSTALAÇÃO DE DEPENDÊNCIAS OPCIONAIS ==========
def install_optional_requirements():
    """Instala dependências opcionais para visão computacional"""
    optional_requirements = [
        "opencv-python",
        "pyautogui",
        "pillow",
        "numpy",
        "pywin32"
    ]

    print("🔧 Instalando dependências opcionais para visão computacional...")
    print("   (Pressione Ctrl+C para pular e usar apenas DirectInput básico)")

    try:
        for package in optional_requirements:
            try:
                __import__(package.split('-')[0])
                print(f"✅ {package} já instalado")
            except ImportError:
                print(f"📦 Instalando {package}...")
                import subprocess
                subprocess.check_call(["pip", "install", package])

        print("✅ Dependências opcionais instaladas! Reinicie o programa para usar visão computacional.")

    except KeyboardInterrupt:
        print("\n⚡ Instalação cancelada - usando modo básico (apenas DirectInput)")
    except Exception as e:
        print(f"⚠️  Erro na instalação: {e}")
        print("   Continuando em modo básico...")

# ========== EXECUÇÃO PRINCIPAL ==========
if __name__ == "__main__":
    print("🚀 LoL DirectInput Bot")
    print("=" * 50)

    # Verifica se precisa instalar dependências opcionais
    if not VISION_AVAILABLE:
        print("⚠️  Dependências de visão computacional não encontradas.")
        print("   Você pode:")
        print("   1. Usar o bot em modo básico (apenas DirectInput)")
        print("   2. Instalar dependências opcionais para visão computacional")
        print()

        choice = input("Instalar dependências opcionais? (s/N): ").lower().strip()
        if choice in ['s', 'sim', 'y', 'yes']:
            try:
                install_optional_requirements()
            except:
                print("⚠️  Erro na instalação, continuando em modo básico...")
        else:
            print("⚡ Continuando em modo básico...")
        print()

    # Executa o sistema
    try:
        bot = LoLAutoFollow()
        bot.run()
    except KeyboardInterrupt:
        print("\n👋 Programa interrompido pelo usuário")
    except Exception as e:
        print(f"\n❌ Erro: {e}")
    finally:
        input("\nPressione Enter para sair...")