#!/usr/bin/env python3
"""
Análise melhorada das cores da imagem life.png
"""

import cv2
import numpy as np
from PIL import Image
import os

def analyze_life_colors_improved():
    """Análise melhorada das cores da life.png"""
    
    if not os.path.exists('life.png'):
        print("❌ Arquivo life.png não encontrado!")
        return None
    
    # Carrega a imagem
    pil_image = Image.open('life.png')
    img_array = np.array(pil_image)
    
    # Remove canal alpha se existir
    if img_array.shape[2] == 4:
        img_array = img_array[:, :, :3]
    
    # Converte para BGR (OpenCV)
    img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)
    img_hsv = cv2.cvtColor(img_bgr, cv2.COLOR_BGR2HSV)
    
    print(f"📊 Dimensões: {img_bgr.shape}")
    
    # Analisa cores dominantes
    pixels_bgr = img_bgr.reshape(-1, 3)
    pixels_hsv = img_hsv.reshape(-1, 3)
    
    # Remove pixels pretos (fundo)
    non_black_mask = np.any(pixels_bgr > 10, axis=1)
    pixels_bgr_filtered = pixels_bgr[non_black_mask]
    pixels_hsv_filtered = pixels_hsv[non_black_mask]
    
    print(f"📊 Pixels não-pretos: {len(pixels_bgr_filtered)}")
    
    # Encontra cores únicas
    unique_colors_bgr = np.unique(pixels_bgr_filtered, axis=0)
    
    print(f"📊 Cores únicas (sem preto): {len(unique_colors_bgr)}")
    
    # Calcula estatísticas HSV
    h_values = pixels_hsv_filtered[:, 0]
    s_values = pixels_hsv_filtered[:, 1] 
    v_values = pixels_hsv_filtered[:, 2]
    
    print(f"\n🎨 Estatísticas HSV:")
    print(f"  Hue (H): min={h_values.min()}, max={h_values.max()}, média={h_values.mean():.1f}")
    print(f"  Saturation (S): min={s_values.min()}, max={s_values.max()}, média={s_values.mean():.1f}")
    print(f"  Value (V): min={v_values.min()}, max={v_values.max()}, média={v_values.mean():.1f}")
    
    # Cria ranges baseados nas estatísticas
    h_min, h_max = int(h_values.min()), int(h_values.max())
    s_min, s_max = int(s_values.min()), int(s_values.max())
    v_min, v_max = int(v_values.min()), int(v_values.max())
    
    # Adiciona tolerância
    h_tolerance = 5
    s_tolerance = 30
    v_tolerance = 30
    
    life_color_range = {
        'name': 'life_health_bar',
        'lower_hsv': np.array([
            max(0, h_min - h_tolerance),
            max(0, s_min - s_tolerance), 
            max(0, v_min - v_tolerance)
        ]),
        'upper_hsv': np.array([
            min(179, h_max + h_tolerance),
            min(255, s_max + s_tolerance),
            min(255, v_max + v_tolerance)
        ]),
        'description': 'Cores da barra de vida dos aliados'
    }
    
    print(f"\n🎯 Range de cores da vida:")
    print(f"  Lower HSV: {life_color_range['lower_hsv']}")
    print(f"  Upper HSV: {life_color_range['upper_hsv']}")
    
    return life_color_range

def create_life_colors_config(color_range):
    """Cria arquivo de configuração das cores"""
    
    config_content = f'''import numpy as np

# ========== CONFIGURAÇÃO DAS CORES DA BARRA DE VIDA ==========
# Baseado na análise da imagem life.png

LIFE_COLOR_RANGE = {{
    'name': '{color_range['name']}',
    'lower_hsv': np.array({list(color_range['lower_hsv'])}),
    'upper_hsv': np.array({list(color_range['upper_hsv'])}),
    'description': '{color_range['description']}'
}}

# Range alternativo mais amplo para detecção
LIFE_COLOR_RANGE_BROAD = {{
    'name': 'life_health_bar_broad',
    'lower_hsv': np.array([85, 100, 100]),  # Azul-ciano com boa saturação
    'upper_hsv': np.array([115, 255, 255]), # Cobertura ampla de azuis
    'description': 'Range amplo para barras de vida azuis'
}}

# Função para testar se uma cor está no range
def is_life_color(hsv_pixel, use_broad=False):
    """
    Verifica se um pixel HSV corresponde às cores da barra de vida
    
    Args:
        hsv_pixel: Pixel no formato HSV (H, S, V)
        use_broad: Se True, usa o range mais amplo
    
    Returns:
        bool: True se a cor corresponde à barra de vida
    """
    color_range = LIFE_COLOR_RANGE_BROAD if use_broad else LIFE_COLOR_RANGE
    
    return (color_range['lower_hsv'][0] <= hsv_pixel[0] <= color_range['upper_hsv'][0] and
            color_range['lower_hsv'][1] <= hsv_pixel[1] <= color_range['upper_hsv'][1] and
            color_range['lower_hsv'][2] <= hsv_pixel[2] <= color_range['upper_hsv'][2])
'''
    
    with open('life_colors_final.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✅ Configuração salva em 'life_colors_final.py'")

if __name__ == "__main__":
    print("🔍 Análise melhorada das cores da life.png...")
    print("=" * 60)
    
    color_range = analyze_life_colors_improved()
    
    if color_range:
        create_life_colors_config(color_range)
        print("\n✅ Análise concluída!")
    else:
        print("❌ Falha na análise")
