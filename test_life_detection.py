#!/usr/bin/env python3
"""
Script de teste para verificar a detecção de barras de vida
"""

import cv2
import numpy as np
from PIL import ImageGrab
import time

# Configuração das cores da barra de vida (mesmo do bot)
LIFE_COLOR_RANGES = [
    {
        'name': 'life_primary',
        'lower_hsv': np.array([95, 150, 150]),  # Azul-ciano principal
        'upper_hsv': np.array([105, 255, 255]),
        'description': 'Cor principal da barra de vida'
    },
    {
        'name': 'life_secondary', 
        'lower_hsv': np.array([90, 120, 120]),  # Range mais amplo
        'upper_hsv': np.array([110, 255, 255]),
        'description': 'Cor secundária da barra de vida'
    },
    {
        'name': 'life_broad',
        'lower_hsv': np.array([85, 100, 100]),  # Range muito amplo para capturar variações
        'upper_hsv': np.array([115, 255, 255]),
        'description': 'Range amplo para barras de vida azuis'
    }
]

def find_life_bars(image):
    """Encontra barras de vida na imagem"""
    if image is None:
        return [], None

    # Converte para HSV
    hsv = cv2.cvtColor(image, cv2.COLOR_BGR2HSV)
    
    # Cria máscara combinada
    combined_mask = np.zeros(hsv.shape[:2], dtype=np.uint8)
    
    for color_range in LIFE_COLOR_RANGES:
        mask = cv2.inRange(hsv, color_range['lower_hsv'], color_range['upper_hsv'])
        combined_mask = cv2.bitwise_or(combined_mask, mask)
    
    # Operações morfológicas
    kernel = np.ones((3, 3), np.uint8)
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)

    # Encontra contornos
    contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    life_bars = []
    for contour in contours:
        area = cv2.contourArea(contour)
        if area > 20:
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h if h > 0 else 0
            if 1.5 <= aspect_ratio <= 10:
                center_x, center_y = x + w//2, y + h//2
                life_bars.append({
                    'center': (center_x, center_y),
                    'bbox': (x, y, w, h),
                    'area': area,
                    'aspect_ratio': aspect_ratio
                })

    life_bars.sort(key=lambda x: x['area'], reverse=True)
    return life_bars, combined_mask

def test_life_detection():
    """Testa a detecção de barras de vida"""
    print("🔍 Teste de Detecção de Barras de Vida")
    print("=" * 50)
    print("Instruções:")
    print("1. Abra o League of Legends")
    print("2. Entre em uma partida onde você possa ver aliados")
    print("3. Pressione ESPAÇO para capturar e analisar")
    print("4. Pressione ESC para sair")
    print("=" * 50)
    
    while True:
        # Verifica teclas
        key = cv2.waitKey(1) & 0xFF
        
        if key == 27:  # ESC
            print("👋 Saindo...")
            break
        elif key == 32:  # ESPAÇO
            print("\n📸 Capturando tela...")
            
            # Captura a tela
            screenshot = ImageGrab.grab()
            img_bgr = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            
            # Redimensiona para visualização (opcional)
            height, width = img_bgr.shape[:2]
            if width > 1920:
                scale = 1920 / width
                new_width = int(width * scale)
                new_height = int(height * scale)
                img_bgr = cv2.resize(img_bgr, (new_width, new_height))
            
            # Detecta barras de vida
            life_bars, mask = find_life_bars(img_bgr)
            
            print(f"🎯 Barras de vida encontradas: {len(life_bars)}")
            
            # Desenha as detecções na imagem
            result_img = img_bgr.copy()
            
            for i, life_bar in enumerate(life_bars[:10]):  # Mostra apenas as 10 primeiras
                x, y, w, h = life_bar['bbox']
                center = life_bar['center']
                
                # Desenha retângulo
                cv2.rectangle(result_img, (x, y), (x + w, y + h), (0, 255, 0), 2)
                
                # Desenha centro
                cv2.circle(result_img, center, 5, (0, 0, 255), -1)
                
                # Adiciona texto
                text = f"{i+1}: A={life_bar['area']:.0f} R={life_bar['aspect_ratio']:.1f}"
                cv2.putText(result_img, text, (x, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                
                print(f"  {i+1}. Centro: {center}, Área: {life_bar['area']:.0f}, Proporção: {life_bar['aspect_ratio']:.1f}")
            
            # Mostra as imagens
            cv2.imshow('Detecção de Barras de Vida', result_img)
            cv2.imshow('Máscara de Cores', mask)
            
            print("✅ Imagens atualizadas. Pressione ESPAÇO para nova captura ou ESC para sair.")
        
        time.sleep(0.1)
    
    cv2.destroyAllWindows()

if __name__ == "__main__":
    try:
        test_life_detection()
    except KeyboardInterrupt:
        print("\n👋 Teste interrompido")
    except Exception as e:
        print(f"❌ Erro: {e}")
    finally:
        cv2.destroyAllWindows()
