import numpy as np


# ========== CORES DA IMAGEM LIFE.PNG ==========
LIFE_COLOR_RANGES = [
    {
        'name': 'life_color_1',
        'lower_hsv': np.array([np.uint8(90), np.uint8(174), np.uint8(148)]),
        'upper_hsv': np.array([np.uint8(110), np.uint8(18), np.uint8(248)]),
        'bgr': (np.uint8(198), np.uint8(138), np.uint8(24)),
        'percentage': 15.8
    },
    {
        'name': 'life_color_2',
        'lower_hsv': np.array([np.uint8(92), np.uint8(144), np.uint8(189)]),
        'upper_hsv': np.array([np.uint8(112), np.uint8(244), np.uint8(33)]),
        'bgr': (np.uint8(239), np.uint8(166), np.uint8(57)),
        'percentage': 8.1
    },
    {
        'name': 'life_color_3',
        'lower_hsv': np.array([np.uint8(90), np.uint8(129), np.uint8(172)]),
        'upper_hsv': np.array([np.uint8(110), np.uint8(229), np.uint8(16)]),
        'bgr': (np.uint8(222), np.uint8(170), np.uint8(66)),
        'percentage': 7.8
    },
    {
        'name': 'life_color_4',
        'lower_hsv': np.array([np.uint8(89), np.uint8(140), np.uint8(172)]),
        'upper_hsv': np.array([np.uint8(109), np.uint8(240), np.uint8(16)]),
        'bgr': (np.uint8(222), np.uint8(170), np.uint8(57)),
        'percentage': 5.4
    },
    {
        'name': 'life_color_5',
        'lower_hsv': np.array([np.uint8(92), np.uint8(146), np.uint8(175)]),
        'upper_hsv': np.array([np.uint8(112), np.uint8(246), np.uint8(19)]),
        'bgr': (np.uint8(225), np.uint8(156), np.uint8(52)),
        'percentage': 5.1
    },
]
